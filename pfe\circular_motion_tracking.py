#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
圆形运动追踪实验
基于continuous_reading实验框架，显示沿屏幕逆时针运动的原点
"""

import os
import sys
import time
import math
from datetime import datetime
from typing import Optional

# ==================== 配置参数 ====================
# 运动模式选择
MOTION_MODE = "snake"        # 运动模式: "circular" 或 "snake"

# 圆形运动参数
RADIUS = 300                    # 运动半径（像素）
ANGULAR_VELOCITY = 1.0          # 角速度（弧度/秒）
NUM_CIRCLES = 3                 # 运动圈数

# 蛇形运动参数
SNAKE_SPEED = 250               # 蛇形运动速度（像素/秒）
SNAKE_ROWS = 5                # 蛇形运动行数
SNAKE_SCREEN_RATIO = 0.5        # 蛇形运动屏幕使用比例（0.1-1.0）

# 通用参数
FPS = 60                        # 刷新率（帧/秒）

# 显示参数
DOT_SIZE = 20                   # 原点大小（像素）
DOT_COLOR = 'white'             # 原点颜色
BACKGROUND_COLOR = 'gray'       # 背景颜色

# EyeLink参数
USE_EYELINK = True              # 是否使用EyeLink
DUMMY_MODE = True                # 是否使用虚拟模式
SCREEN_SIZE = (1920, 1080)      # 屏幕分辨率
ENABLE_CAMERA = False            # 是否启用摄像头录像
CAMERA_INDEX = 2                # 摄像头索引

# 实验参数
PARTICIPANT_ID = "test_motion"  # 默认被试ID
FULLSCREEN = True               # 是否全屏显示

# ==================== 导入模块 ====================
# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

try:
    from psychopy import visual, core, event
    PSYCHOPY_AVAILABLE = True
except ImportError:
    PSYCHOPY_AVAILABLE = False
    print("警告：PsychoPy未安装，将使用模拟模式")

# 导入主项目模块
try:
    from eyelink_manager import EyeLinkManager
    from data_manager import DataManager
    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"模块导入失败: {e}")
    MODULES_AVAILABLE = False

class CircularMotionExperiment:
    """圆形运动追踪实验主控制类"""
    
    def __init__(self, participant_id: str = PARTICIPANT_ID, use_eyelink: bool = USE_EYELINK, 
                 fullscreen: bool = FULLSCREEN):
        """
        初始化实验
        
        Args:
            participant_id: 被试ID
            use_eyelink: 是否使用EyeLink
            fullscreen: 是否全屏显示
        """
        self.participant_id = participant_id
        self.use_eyelink = use_eyelink and MODULES_AVAILABLE
        self.fullscreen = fullscreen
        self.screen_size = SCREEN_SIZE
        
        # 实验组件
        self.win = None
        self.eyelink = None
        self.data_manager = None
        
        # 运动参数
        self.center_x = SCREEN_SIZE[0] // 2
        self.center_y = SCREEN_SIZE[1] // 2
        self.total_duration = self.get_total_duration()
        self.frame_duration = 1.0 / FPS
        
        # 实验数据
        self.data_dir = ""
        self.experiment_start_time = 0.0
        
        # 创建数据目录
        self._create_data_directory()
    
    def _create_data_directory(self):
        """创建数据保存目录"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        data_dir_name = f"{timestamp}_{self.participant_id}_{MOTION_MODE}_motion"
        self.data_dir = os.path.join(root_dir, "data", data_dir_name)
        
        try:
            os.makedirs(self.data_dir, exist_ok=True)
            print(f"数据将保存到: {self.data_dir}")
        except Exception as e:
            print(f"创建数据目录失败: {e}")
            # 确保data_dir不为None
            fallback_dir = os.path.join(root_dir, "data")
            os.makedirs(fallback_dir, exist_ok=True)
            self.data_dir = fallback_dir
    
    def initialize_components(self) -> bool:
        """
        初始化所有实验组件
        
        Returns:
            初始化是否成功
        """
        print("初始化实验组件...")
        
        # 初始化显示窗口
        if not self._initialize_display():
            return False
        
        # 初始化EyeLink
        if self.use_eyelink:
            if not self._initialize_eyelink():
                return False
        
        # 初始化数据管理器
        if not self._initialize_data_manager():
            return False
        
        return True
    
    def _initialize_display(self) -> bool:
        """初始化显示窗口"""
        if not PSYCHOPY_AVAILABLE:
            print("PsychoPy不可用，使用模拟模式")
            return True
        
        try:
            from psychopy import monitors
            
            # 设置显示器
            mon = monitors.Monitor('projMonitor', width=53.0, distance=70.0)
            mon.setSizePix(self.screen_size)
            
            # 创建窗口
            self.win = visual.Window(
                self.screen_size,
                fullscr=self.fullscreen,
                screen=1,
                monitor=mon,
                winType='pyglet',
                units='pix',
                color=BACKGROUND_COLOR
            )
            
            print("✓ 显示窗口初始化成功")
            return True
            
        except Exception as e:
            print(f"✗ 显示窗口初始化失败: {e}")
            return False
    
    def _initialize_eyelink(self) -> bool:
        """初始化EyeLink"""
        try:
            self.eyelink = EyeLinkManager(
                participant_id=self.participant_id,
                data_dir=self.data_dir,
                screen_size=self.screen_size,
                dummy_mode=DUMMY_MODE,
                display_window=self.win,
                enable_camera=ENABLE_CAMERA,
                camera_index=CAMERA_INDEX
            )
            
            if self.eyelink.connect():
                print("✓ EyeLink连接成功")
                if ENABLE_CAMERA:
                    print("✓ 摄像头录像功能已启用")
                if self.eyelink.setup_tracker():
                    print("✓ EyeLink配置成功")
                    if self.eyelink.setup_graphics_environment():
                        print("✓ EyeLink图形环境设置成功")
                return True
            else:
                print("警告：EyeLink连接失败")
                return False
                
        except Exception as e:
            print(f"✗ EyeLink初始化失败: {e}")
            return False
    
    def _initialize_data_manager(self) -> bool:
        """初始化数据管理器"""
        try:
            # 创建简化的数据管理器
            class SimpleDataManager:
                def __init__(self, data_dir, participant_id):
                    self.data_dir = data_dir
                    self.participant_id = participant_id
                    self.log_file = os.path.join(data_dir, "experiment_log.txt")
                    self._init_log()
                
                def _init_log(self):
                    try:
                        with open(self.log_file, 'w', encoding='utf-8') as f:
                            f.write(f"圆形运动追踪实验日志 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                            f.write("=" * 50 + "\n\n")
                    except Exception as e:
                        print(f"初始化日志失败: {e}")
                
                def log_event(self, event: str, details: str = ""):
                    timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                    log_entry = f"[{timestamp}] {event}"
                    if details:
                        log_entry += f" - {details}"
                    log_entry += "\n"
                    
                    try:
                        with open(self.log_file, 'a', encoding='utf-8') as f:
                            f.write(log_entry)
                    except Exception as e:
                        print(f"写入日志失败: {e}")
            
            self.data_manager = SimpleDataManager(self.data_dir, self.participant_id)
            print("✓ 数据管理器初始化成功")
            return True
            
        except Exception as e:
            print(f"✗ 数据管理器初始化失败: {e}")
            return False
    
    def run_calibration(self) -> bool:
        """
        运行EyeLink校准
        
        Returns:
            校准是否成功
        """
        if not self.use_eyelink:
            print("未使用EyeLink，跳过校准")
            return True
        
        print("\n开始EyeLink校准...")
        
        try:
            self.eyelink.log_calibration_start()
            calibration_success = self.eyelink.calibrate()
            self.eyelink.log_calibration_end(calibration_success)
            
            if calibration_success:
                print("✓ EyeLink校准成功")
                return True
            else:
                print("✗ EyeLink校准失败")
                return False
                
        except Exception as e:
            print(f"校准过程出错: {e}")
            return False
    
    def calculate_position(self, elapsed_time: float) -> tuple:
        """
        计算当前时间点的圆点位置（根据运动模式）

        Args:
            elapsed_time: 从开始运动经过的时间（秒）

        Returns:
            (x, y): 圆点位置坐标
        """
        if MOTION_MODE == "circular":
            return self.calculate_circular_position(elapsed_time)
        elif MOTION_MODE == "snake":
            return self.calculate_snake_position(elapsed_time)
        else:
            raise ValueError(f"未知的运动模式: {MOTION_MODE}")

    def calculate_circular_position(self, elapsed_time: float) -> tuple:
        """
        计算圆形运动位置

        Args:
            elapsed_time: 从开始运动经过的时间（秒）

        Returns:
            (x, y): 圆点位置坐标
        """
        angle = ANGULAR_VELOCITY * elapsed_time
        x = self.center_x + RADIUS * math.cos(angle)
        y = self.center_y + RADIUS * math.sin(angle)
        return (x, y)

    def calculate_snake_position(self, elapsed_time: float) -> tuple:
        """
        计算蛇形运动位置

        Args:
            elapsed_time: 从开始运动经过的时间（秒）

        Returns:
            (x, y): 圆点位置坐标
        """
        # 计算蛇形运动区域
        screen_width, screen_height = self.screen_size
        motion_width = screen_width * SNAKE_SCREEN_RATIO
        motion_height = screen_height * SNAKE_SCREEN_RATIO

        # 计算起始位置（左上角，考虑屏幕比例）
        start_x = (screen_width - motion_width) / 2
        start_y = (screen_height - motion_height) / 2

        # 计算每行的高度
        row_height = motion_height / SNAKE_ROWS

        # 计算总距离和当前距离
        total_distance = SNAKE_SPEED * elapsed_time

        # 计算当前在第几行
        distance_per_row = motion_width
        total_row_distance = 0
        current_row = 0

        for row in range(SNAKE_ROWS):
            if total_distance <= total_row_distance + distance_per_row:
                current_row = row
                break
            total_row_distance += distance_per_row
        else:
            # 如果超出了所有行，停在最后一个位置
            current_row = SNAKE_ROWS - 1
            total_row_distance = (SNAKE_ROWS - 1) * distance_per_row

        # 计算在当前行的位置
        distance_in_row = total_distance - total_row_distance

        # 计算Y坐标
        y = start_y + current_row * row_height

        # 计算X坐标（奇数行从左到右，偶数行从右到左）
        if current_row % 2 == 0:  # 偶数行（从左到右）
            x = start_x + distance_in_row
        else:  # 奇数行（从右到左）
            x = start_x + motion_width - distance_in_row

        # 确保坐标在有效范围内
        x = max(start_x, min(start_x + motion_width, x))
        y = max(start_y, min(start_y + motion_height, y))

        return (x, y)

    def get_total_duration(self) -> float:
        """
        获取运动总时长

        Returns:
            总时长（秒）
        """
        if MOTION_MODE == "circular":
            return NUM_CIRCLES * 2 * math.pi / ANGULAR_VELOCITY
        elif MOTION_MODE == "snake":
            screen_width = self.screen_size[0]
            motion_width = screen_width * SNAKE_SCREEN_RATIO
            total_distance = motion_width * SNAKE_ROWS
            return total_distance / SNAKE_SPEED
        else:
            return 10.0  # 默认值

    def run_motion_tracking(self) -> bool:
        """
        运行运动追踪（圆形或蛇形）

        Returns:
            是否成功完成
        """
        if not PSYCHOPY_AVAILABLE:
            print(f"模拟模式：运行{MOTION_MODE}运动追踪")
            if MOTION_MODE == "circular":
                print(f"运动参数：半径={RADIUS}px, 角速度={ANGULAR_VELOCITY}rad/s, 圈数={NUM_CIRCLES}")
            elif MOTION_MODE == "snake":
                print(f"运动参数：速度={SNAKE_SPEED}px/s, 行数={SNAKE_ROWS}, 屏幕比例={SNAKE_SCREEN_RATIO}")
            print(f"总时长：{self.total_duration:.2f}秒")
            time.sleep(min(self.total_duration, 5))  # 模拟模式下缩短时间
            return True

        try:
            # 创建圆点
            dot = visual.Circle(
                self.win,
                radius=DOT_SIZE // 2,
                fillColor=DOT_COLOR,
                lineColor=DOT_COLOR,
                units='pix'
            )

            # 开始眼动记录
            if self.eyelink:
                if not self.eyelink.start_recording(1):
                    print("警告：眼动记录启动失败")
                self.eyelink.send_message(f"{MOTION_MODE.upper()}_TRACKING_START")
                if self.data_manager:
                    if MOTION_MODE == "circular":
                        self.data_manager.log_event("MOTION_START", f"模式=圆形, 半径={RADIUS}, 角速度={ANGULAR_VELOCITY}, 圈数={NUM_CIRCLES}")
                    elif MOTION_MODE == "snake":
                        self.data_manager.log_event("MOTION_START", f"模式=蛇形, 速度={SNAKE_SPEED}, 行数={SNAKE_ROWS}, 屏幕比例={SNAKE_SCREEN_RATIO}")

            # 运动循环
            start_time = time.time()
            frame_count = 0

            print(f"开始{MOTION_MODE}运动追踪...")
            if MOTION_MODE == "circular":
                print(f"运动参数：半径={RADIUS}px, 角速度={ANGULAR_VELOCITY}rad/s, 圈数={NUM_CIRCLES}")
            elif MOTION_MODE == "snake":
                print(f"运动参数：速度={SNAKE_SPEED}px/s, 行数={SNAKE_ROWS}, 屏幕比例={SNAKE_SCREEN_RATIO}")
            print(f"预计总时长：{self.total_duration:.2f}秒")

            while True:
                current_time = time.time()
                elapsed_time = current_time - start_time

                # 检查是否完成指定圈数
                if elapsed_time >= self.total_duration:
                    break

                # 计算当前位置
                x, y = self.calculate_position(elapsed_time)

                # 转换为PsychoPy坐标系（中心为原点）
                psychopy_x = x - self.center_x
                psychopy_y = -(y - self.center_y)  # Y轴翻转

                # 更新圆点位置
                dot.pos = (psychopy_x, psychopy_y)

                # 清屏并绘制
                self.win.clearBuffer()
                dot.draw()
                self.win.flip()

                # 发送位置信息到EyeLink
                if self.eyelink and frame_count % 10 == 0:  # 每10帧发送一次消息
                    circles_completed = elapsed_time * ANGULAR_VELOCITY / (2 * math.pi)
                    self.eyelink.send_message(f"DOT_POS {x:.1f} {y:.1f} CIRCLES {circles_completed:.2f}")

                # 检查退出键
                keys = event.getKeys()
                if 'escape' in keys:
                    print("用户按ESC键退出")
                    return False

                frame_count += 1

                # 控制帧率
                target_time = start_time + frame_count * self.frame_duration
                sleep_time = target_time - time.time()
                if sleep_time > 0:
                    time.sleep(sleep_time)

            # 停止眼动记录
            if self.eyelink:
                self.eyelink.send_message(f"{MOTION_MODE.upper()}_TRACKING_END")
                self.eyelink.stop_recording()
                if self.data_manager:
                    self.data_manager.log_event("MOTION_END", f"总时长={elapsed_time:.2f}秒, 帧数={frame_count}")

            print(f"✓ {MOTION_MODE}运动追踪完成")
            print(f"实际运行时长：{elapsed_time:.2f}秒，总帧数：{frame_count}")

            return True

        except Exception as e:
            print(f"运动追踪过程出错: {e}")
            return False

    def show_instructions(self) -> bool:
        """
        显示实验说明

        Returns:
            用户是否继续实验
        """
        if not PSYCHOPY_AVAILABLE:
            print("模拟模式：显示实验说明")
            time.sleep(1)
            return True

        try:
            if MOTION_MODE == "circular":
                instructions = f"""
圆形运动追踪实验

在本实验中，您将看到一个白色圆点沿着屏幕中心做圆形运动。

实验参数：
- 运动模式：圆形运动
- 运动半径：{RADIUS} 像素
- 运动速度：{ANGULAR_VELOCITY:.1f} 弧度/秒
- 运动圈数：{NUM_CIRCLES} 圈
- 预计时长：{self.total_duration:.1f} 秒

请注视运动的圆点，实验过程中请尽量保持头部稳定。

按回车键开始实验，按ESC键可随时退出...
"""
            elif MOTION_MODE == "snake":
                instructions = f"""
蛇形运动追踪实验

在本实验中，您将看到一个白色圆点从屏幕左上角开始，
按蛇形路径（横着走，到头后换行继续）移动到屏幕底部。

实验参数：
- 运动模式：蛇形运动
- 运动速度：{SNAKE_SPEED} 像素/秒
- 运动行数：{SNAKE_ROWS} 行
- 屏幕使用比例：{SNAKE_SCREEN_RATIO*100:.0f}%
- 预计时长：{self.total_duration:.1f} 秒

请注视运动的圆点，实验过程中请尽量保持头部稳定。

按回车键开始实验，按ESC键可随时退出...
"""
            else:
                instructions = f"""
运动追踪实验

预计时长：{self.total_duration:.1f} 秒

请注视运动的圆点，实验过程中请尽量保持头部稳定。

按回车键开始实验，按ESC键可随时退出...
"""

            instruction_text = visual.TextStim(
                self.win,
                text=instructions,
                font='SimHei',
                pos=(0, 0),
                height=30,
                color='white',
                wrapWidth=1200
            )

            # 清空按键缓存
            event.clearEvents()

            while True:
                instruction_text.draw()
                self.win.flip()

                keys = event.getKeys()
                if 'escape' in keys:
                    return False
                elif 'return' in keys:
                    return True

        except Exception as e:
            print(f"显示说明失败: {e}")
            return False

    def run_experiment(self) -> bool:
        """
        运行完整实验

        Returns:
            实验是否成功完成
        """
        print(f"\n开始圆形运动追踪实验 - 被试ID: {self.participant_id}")
        print("=" * 60)

        # 初始化组件
        if not self.initialize_components():
            print("实验初始化失败")
            return False

        # 保存实验设置
        self._save_experiment_settings()

        # EyeLink校准
        if not self.run_calibration():
            print("EyeLink校准失败")
            return False

        # 显示实验说明
        if not self.show_instructions():
            print("用户跳过了实验说明")
            return False

        # 记录实验开始
        self.experiment_start_time = time.time()
        if self.data_manager:
            self.data_manager.log_event("EXPERIMENT_START")

        # 运行运动追踪
        try:
            success = self.run_motion_tracking()

            if success:
                # 显示实验结束
                if PSYCHOPY_AVAILABLE and self.win:
                    end_text = visual.TextStim(
                        self.win,
                        text="实验结束，谢谢参与！",
                        font='SimHei',
                        pos=(0, 0),
                        height=40,
                        color='white'
                    )

                    for _ in range(180):  # 显示3秒
                        end_text.draw()
                        self.win.flip()
                        time.sleep(0.016)
                else:
                    print("实验结束，谢谢参与！")
                    time.sleep(2)
            else:
                print("实验被中断")

            if self.data_manager:
                self.data_manager.log_event("EXPERIMENT_END")

            return success

        except Exception as e:
            print(f"实验运行出错: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _save_experiment_settings(self):
        """保存实验设置"""
        import json

        settings = {
            'participant_id': self.participant_id,
            'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            'use_eyelink': self.use_eyelink,
            'fullscreen': self.fullscreen,
            'screen_size': self.screen_size,
            'motion_mode': MOTION_MODE,
            'motion_parameters': {
                'mode': MOTION_MODE,
                'total_duration': self.total_duration,
                'fps': FPS
            },
            'display_parameters': {
                'dot_size': DOT_SIZE,
                'dot_color': DOT_COLOR,
                'background_color': BACKGROUND_COLOR
            },
            'eyelink_parameters': {
                'dummy_mode': DUMMY_MODE,
                'enable_camera': ENABLE_CAMERA,
                'camera_index': CAMERA_INDEX
            }
        }

        # 添加特定运动模式的参数
        if MOTION_MODE == "circular":
            settings['motion_parameters'].update({
                'radius': RADIUS,
                'angular_velocity': ANGULAR_VELOCITY,
                'num_circles': NUM_CIRCLES
            })
        elif MOTION_MODE == "snake":
            settings['motion_parameters'].update({
                'speed': SNAKE_SPEED,
                'rows': SNAKE_ROWS,
                'screen_ratio': SNAKE_SCREEN_RATIO
            })

        try:
            settings_file = os.path.join(self.data_dir, "experiment_settings.json")
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
            print(f"实验设置已保存到 {settings_file}")
        except Exception as e:
            print(f"保存实验设置失败: {e}")

    def cleanup(self):
        """清理资源"""
        print("清理资源...")

        # 关闭EyeLink连接
        if self.eyelink:
            try:
                self.eyelink.close()
                print("✓ EyeLink连接已关闭")
            except Exception as e:
                print(f"关闭EyeLink连接时出错: {e}")

        # 关闭显示窗口
        if self.win:
            try:
                self.win.close()
                print("✓ 显示窗口已关闭")
            except Exception as e:
                print(f"关闭显示窗口时出错: {e}")

        print("资源清理完成")

def main():
    """主函数 - 运行实验"""
    print("圆形运动追踪实验")
    print("=" * 40)

    # 获取被试信息
    participant_id = input("请输入被试ID: ").strip()
    if not participant_id:
        participant_id = f"test_{datetime.now().strftime('%H%M%S')}"
        print(f"使用默认ID: {participant_id}")

    # 实验设置
    use_eyelink = input("是否使用EyeLink? (y/n, 默认y): ").strip().lower() != 'n'
    fullscreen = input("是否全屏显示? (y/n, 默认y): ").strip().lower() != 'n'

    # 创建实验对象
    experiment = CircularMotionExperiment(
        participant_id=participant_id,
        use_eyelink=use_eyelink,
        fullscreen=fullscreen
    )

    try:
        # 运行实验
        success = experiment.run_experiment()

        if success:
            print("\n✓ 实验成功完成！")
        else:
            print("\n✗ 实验未能完成")

    except KeyboardInterrupt:
        print("\n实验被用户中断")
    except Exception as e:
        print(f"\n实验出现错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理资源
        experiment.cleanup()

if __name__ == "__main__":
    main()
